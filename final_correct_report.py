#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成最终的正确分析报告
"""

import pandas as pd
import numpy as np
from datetime import datetime

def generate_final_correct_report():
    """生成最终的正确分析报告"""
    
    # 读取正确的分析结果
    df = pd.read_csv('correct_analysis_results.csv')
    
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("永祥BC工段3s数据最终正确分析报告")
    report_lines.append("=" * 80)
    report_lines.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    report_lines.append("数据处理概览:")
    report_lines.append("-" * 40)
    report_lines.append("数据来源: 网络路径永祥BC工段3s数据")
    report_lines.append("处理策略: 每个目录选择最新的3个CSV文件")
    report_lines.append("分析流程: 先检查自动定埚位工序完成后的CCD测温值，再分析自动控温工序")
    report_lines.append("")
    
    report_lines.append("数据有效性验证:")
    report_lines.append("-" * 40)
    report_lines.append("验证标准: 自动定埚位工序完成后CCD测温值在1444-1452度范围内")
    ccd_temps = df['CCD_temp_at_crucible_completion']
    report_lines.append(f"有效数据量: {len(df)} 个自动控温工序")
    report_lines.append(f"CCD测温范围: {ccd_temps.min():.1f} - {ccd_temps.max():.1f}度")
    report_lines.append(f"CCD测温平均值: {ccd_temps.mean():.1f}度")
    report_lines.append(f"CCD测温标准差: {ccd_temps.std():.1f}度")
    report_lines.append("")
    
    report_lines.append("时间差统计分析(副功率关闭到熔液占比达到100%):")
    report_lines.append("-" * 40)
    delta_times = df['delta_T_minutes']
    report_lines.append(f"样本数量: {len(delta_times)}")
    report_lines.append(f"平均值: {delta_times.mean():.2f} 分钟")
    report_lines.append(f"中位数: {delta_times.median():.2f} 分钟")
    report_lines.append(f"标准差: {delta_times.std():.2f} 分钟")
    report_lines.append(f"最小值: {delta_times.min():.2f} 分钟")
    report_lines.append(f"最大值: {delta_times.max():.2f} 分钟")
    report_lines.append(f"第一四分位数(Q1): {delta_times.quantile(0.25):.2f} 分钟")
    report_lines.append(f"第三四分位数(Q3): {delta_times.quantile(0.75):.2f} 分钟")
    report_lines.append("")
    
    # 异常值分析
    q1 = delta_times.quantile(0.25)
    q3 = delta_times.quantile(0.75)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    
    outliers = delta_times[(delta_times < lower_bound) | (delta_times > upper_bound)]
    
    report_lines.append("异常值分析(基于IQR方法):")
    report_lines.append("-" * 40)
    report_lines.append(f"正常值范围: [{lower_bound:.2f}, {upper_bound:.2f}] 分钟")
    report_lines.append(f"异常值数量: {len(outliers)}")
    report_lines.append(f"异常值比例: {len(outliers)/len(delta_times)*100:.2f}%")
    
    if len(outliers) > 0:
        report_lines.append("异常值列表:")
        for i, outlier in enumerate(outliers.head(10)):
            report_lines.append(f"  {outlier:.2f} 分钟")
            if i >= 9 and len(outliers) > 10:
                report_lines.append(f"  ... 还有 {len(outliers)-10} 个异常值")
                break
    report_lines.append("")
    
    report_lines.append("数据分布分析:")
    report_lines.append("-" * 40)
    ranges = [
        (0, 20, "0-20分钟"),
        (20, 40, "20-40分钟"),
        (40, 60, "40-60分钟"),
        (60, 100, "60-100分钟"),
        (100, float('inf'), ">100分钟")
    ]
    
    for min_val, max_val, label in ranges:
        if max_val == float('inf'):
            count = len(delta_times[delta_times >= min_val])
        else:
            count = len(delta_times[(delta_times >= min_val) & (delta_times < max_val)])
        percentage = count / len(delta_times) * 100
        report_lines.append(f"{label}: {count} 个 ({percentage:.1f}%)")
    report_lines.append("")
    
    report_lines.append("结论和建议:")
    report_lines.append("-" * 40)
    report_lines.append(f"• 成功识别了 {len(df)} 个有效的自动控温工序")
    report_lines.append("• 所有数据的CCD测温值都在要求的1444-1452度范围内")
    report_lines.append(f"• 副功率关闭到熔液占比达到100%的典型时间为 {delta_times.median():.1f} 分钟")
    normal_count = len(delta_times[delta_times <= upper_bound])
    report_lines.append(f"• 大部分工序({normal_count/len(delta_times)*100:.1f}%)在 {upper_bound:.1f} 分钟内完成")
    report_lines.append(f"• 存在 {len(outliers)} 个异常值，建议重点关注这些工序")
    report_lines.append("")
    
    report_lines.append("输出文件:")
    report_lines.append("-" * 40)
    report_lines.append("1. correct_analysis_results.csv - 完整的正确分析结果")
    report_lines.append("2. correct_analysis_report.txt - 分析报告")
    report_lines.append("3. correct_analysis.log - 处理日志")
    report_lines.append("4. final_correct_analysis_report.txt - 最终详细报告")
    report_lines.append("")
    
    report_lines.append("=" * 80)
    
    # 保存报告
    report_content = "\n".join(report_lines)
    with open('final_correct_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("最终正确分析报告已生成!")
    print("\n" + report_content)

if __name__ == "__main__":
    generate_final_correct_report()
