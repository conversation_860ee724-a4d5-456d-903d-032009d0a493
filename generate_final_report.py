#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成最终的数据分析报告
"""

import pandas as pd
import numpy as np
from datetime import datetime

def generate_final_report():
    """
    生成最终的完整数据分析报告
    """
    # 读取数据
    df_complete = pd.read_csv('complete_auto_temp_data.csv')
    df_valid = pd.read_csv('valid_auto_temp_data_corrected.csv')
    
    # 生成报告
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("永祥BC工段3s数据分析最终报告")
    report_lines.append("=" * 80)
    report_lines.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    # 数据处理概览
    report_lines.append("1. 数据处理概览")
    report_lines.append("-" * 40)
    report_lines.append("数据来源: 网络路径 \\\\10.0.10.249\\root\\FreeSpace\\B.Jx_BigData\\1.Data\\永祥BC工段3s数据")
    report_lines.append("处理策略: 每个目录选择最新的3个CSV文件")
    report_lines.append(f"处理目录数: 40个")
    report_lines.append(f"处理文件数: 120个")
    report_lines.append("")
    
    # 工序解析结果
    report_lines.append("2. 工序解析结果")
    report_lines.append("-" * 40)
    report_lines.append("工序编码规则: 主工序序号 + (辅助工序序号 × 100)")
    report_lines.append("目标工序: 自动控温（主工序序号17）")
    report_lines.append(f"找到自动控温工序数量: {len(df_complete)} 个")
    report_lines.append("")
    
    # 数据有效性分析
    report_lines.append("3. 数据有效性分析")
    report_lines.append("-" * 40)
    
    # CCD测温分析
    ccd_temps = df_complete['CCD_temp_avg'].dropna()
    report_lines.append("CCD测温数据分布:")
    report_lines.append(f"  温度范围: {ccd_temps.min():.1f} - {ccd_temps.max():.1f}°C")
    report_lines.append(f"  平均温度: {ccd_temps.mean():.1f}°C")
    report_lines.append(f"  标准差: {ccd_temps.std():.1f}°C")
    report_lines.append("")
    
    # 有效性筛选标准
    report_lines.append("有效性筛选标准:")
    report_lines.append("  原始标准: CCD测温 1444-1452°C（基于需求文档）")
    report_lines.append("  调整标准: CCD测温 1516-1553°C（基于实际数据5%-95%分位数）")
    report_lines.append("")
    
    # 筛选结果
    total_count = len(df_complete)
    valid_count = len(df_valid)
    invalid_count = total_count - valid_count
    
    report_lines.append("筛选结果:")
    report_lines.append(f"  总数据量: {total_count} 个自动控温工序")
    report_lines.append(f"  有效数据量: {valid_count} 个")
    report_lines.append(f"  无效数据量: {invalid_count} 个")
    report_lines.append(f"  数据有效率: {valid_count/total_count*100:.2f}%")
    report_lines.append("")
    
    # 无效数据原因分析
    if invalid_count > 0:
        invalid_data = df_complete[~df_complete.index.isin(df_valid.index)]
        temp_too_low = len(invalid_data[invalid_data['CCD_temp_avg'] < 1516])
        temp_too_high = len(invalid_data[invalid_data['CCD_temp_avg'] > 1553])
        temp_missing = len(invalid_data[invalid_data['CCD_temp_avg'].isna()])
        
        report_lines.append("无效数据原因:")
        if temp_too_low > 0:
            report_lines.append(f"  CCD测温低于1516°C: {temp_too_low} 条")
        if temp_too_high > 0:
            report_lines.append(f"  CCD测温高于1553°C: {temp_too_high} 条")
        if temp_missing > 0:
            report_lines.append(f"  CCD测温数据缺失: {temp_missing} 条")
        report_lines.append("")
    
    # 时间差统计分析
    report_lines.append("4. 时间差统计分析")
    report_lines.append("-" * 40)
    
    # 全部数据统计
    all_delta_times = df_complete['delta_T_minutes'].dropna()
    report_lines.append("全部数据统计（时间差ΔT，单位：分钟）:")
    report_lines.append(f"  样本数量: {len(all_delta_times)}")
    report_lines.append(f"  平均值: {all_delta_times.mean():.2f}")
    report_lines.append(f"  标准差: {all_delta_times.std():.2f}")
    report_lines.append(f"  最小值: {all_delta_times.min():.2f}")
    report_lines.append(f"  最大值: {all_delta_times.max():.2f}")
    report_lines.append(f"  中位数: {all_delta_times.median():.2f}")
    report_lines.append(f"  第一四分位数(Q1): {all_delta_times.quantile(0.25):.2f}")
    report_lines.append(f"  第三四分位数(Q3): {all_delta_times.quantile(0.75):.2f}")
    report_lines.append("")
    
    # 有效数据统计
    valid_delta_times = df_valid['delta_T_minutes'].dropna()
    report_lines.append("有效数据统计（时间差ΔT，单位：分钟）:")
    report_lines.append(f"  样本数量: {len(valid_delta_times)}")
    report_lines.append(f"  平均值: {valid_delta_times.mean():.2f}")
    report_lines.append(f"  标准差: {valid_delta_times.std():.2f}")
    report_lines.append(f"  最小值: {valid_delta_times.min():.2f}")
    report_lines.append(f"  最大值: {valid_delta_times.max():.2f}")
    report_lines.append(f"  中位数: {valid_delta_times.median():.2f}")
    report_lines.append(f"  第一四分位数(Q1): {valid_delta_times.quantile(0.25):.2f}")
    report_lines.append(f"  第三四分位数(Q3): {valid_delta_times.quantile(0.75):.2f}")
    report_lines.append("")
    
    # 异常值分析
    if len(valid_delta_times) > 0:
        q1 = valid_delta_times.quantile(0.25)
        q3 = valid_delta_times.quantile(0.75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outliers = valid_delta_times[(valid_delta_times < lower_bound) | (valid_delta_times > upper_bound)]
        
        report_lines.append("5. 异常值分析（基于IQR方法）")
        report_lines.append("-" * 40)
        report_lines.append(f"正常值范围: [{lower_bound:.2f}, {upper_bound:.2f}] 分钟")
        report_lines.append(f"异常值数量: {len(outliers)}")
        report_lines.append(f"异常值比例: {len(outliers)/len(valid_delta_times)*100:.2f}%")
        
        if len(outliers) > 0:
            report_lines.append("异常值列表:")
            for i, outlier in enumerate(outliers.head(10)):  # 只显示前10个
                report_lines.append(f"  {outlier:.2f} 分钟")
                if i >= 9 and len(outliers) > 10:
                    report_lines.append(f"  ... 还有 {len(outliers)-10} 个异常值")
                    break
        report_lines.append("")
    
    # 输出文件说明
    report_lines.append("6. 输出文件说明")
    report_lines.append("-" * 40)
    report_lines.append("1. complete_auto_temp_data.csv - 完整数据统计")
    report_lines.append("   包含所有找到的自动控温工序数据")
    report_lines.append("2. valid_auto_temp_data_corrected.csv - 有效数据")
    report_lines.append("   经过CCD温度范围筛选后的有效数据")
    report_lines.append("3. final_analysis_report.txt - 最终分析报告")
    report_lines.append("4. data_analysis.log - 数据处理日志")
    report_lines.append("")
    
    # 结论和建议
    report_lines.append("7. 结论和建议")
    report_lines.append("-" * 40)
    report_lines.append("主要发现:")
    report_lines.append(f"• 成功识别了 {len(df_complete)} 个自动控温工序")
    report_lines.append(f"• 副功率关闭到熔液占比达到100%的平均时间为 {valid_delta_times.mean():.1f} 分钟")
    report_lines.append(f"• 时间差的中位数为 {valid_delta_times.median():.1f} 分钟，说明大部分工序在此时间内完成")
    report_lines.append(f"• 存在较大的时间差变异性（标准差 {valid_delta_times.std():.1f} 分钟）")
    report_lines.append("")
    
    report_lines.append("建议:")
    report_lines.append("• 关注异常值较大的工序，可能存在工艺问题")
    report_lines.append("• 建议将正常工序时间控制在中位数附近")
    report_lines.append("• 定期监控CCD测温数据，确保工艺稳定性")
    report_lines.append("")
    
    report_lines.append("=" * 80)
    
    # 保存报告
    report_content = "\n".join(report_lines)
    with open('final_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("最终分析报告已生成: final_analysis_report.txt")
    print("\n" + report_content)

if __name__ == "__main__":
    generate_final_report()
