#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查工序字段的实际值
"""

import pandas as pd
import numpy as np
import os
import glob

def check_process_codes(base_path):
    """
    检查工序字段的实际值
    """
    # 获取第一个目录的第一个文件
    subdirs = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]
    
    if not subdirs:
        print("未找到数据目录")
        return
    
    subdir_path = os.path.join(base_path, subdirs[0])
    csv_files = glob.glob(os.path.join(subdir_path, "*.csv"))
    
    if not csv_files:
        print("未找到CSV文件")
        return
    
    # 读取第一个文件
    csv_file = csv_files[0]
    print(f"检查文件: {csv_file}")
    
    try:
        df = pd.read_csv(csv_file, encoding='utf-8')
        print(f"文件记录数: {len(df)}")
        print(f"字段列表: {list(df.columns)}")
        
        # 检查工序字段
        if '工序' in df.columns:
            process_values = df['工序'].dropna().unique()
            print(f"\n工序字段的唯一值 (前20个): {process_values[:20]}")
            print(f"工序字段的数据类型: {df['工序'].dtype}")
            
            # 检查是否有17相关的值（自动控温）
            auto_temp_values = df[df['工序'].astype(str).str.contains('17', na=False)]['工序'].unique()
            print(f"包含'17'的工序值: {auto_temp_values}")
            
            # 检查副功率字段
            if '副功率' in df.columns:
                power_values = df['副功率'].dropna().unique()
                print(f"\n副功率字段的唯一值 (前20个): {power_values[:20]}")
                print(f"副功率为0的记录数: {len(df[df['副功率'] == 0])}")
            
            # 检查熔液占比字段
            if '熔液占比' in df.columns:
                liquid_values = df['熔液占比'].dropna().unique()
                print(f"\n熔液占比字段的唯一值 (前20个): {liquid_values[:20]}")
                print(f"熔液占比为100的记录数: {len(df[df['熔液占比'] == 100])}")
                print(f"熔液占比>=100的记录数: {len(df[df['熔液占比'] >= 100])}")
            
            # 显示前几行数据
            print(f"\n前5行数据:")
            print(df[['time', '工序', '副功率', '熔液占比']].head())
            
        else:
            print("未找到'工序'字段")
            
    except Exception as e:
        print(f"读取文件失败: {e}")

def main():
    base_path = r"\\10.0.10.249\root\FreeSpace\B.Jx_BigData\1.Data\永祥BC工段3s数据"
    check_process_codes(base_path)

if __name__ == "__main__":
    main()
