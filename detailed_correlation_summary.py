#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的相关性分析总结
"""

import pandas as pd
import numpy as np

def detailed_summary():
    # 读取相关性数据
    df = pd.read_csv('correlation_processed_data.csv')
    
    print('剩料重量与时间差相关性分析详细结果')
    print('=' * 60)
    
    # 基本统计
    print('\n基本统计信息:')
    print(f'样本数量: {len(df)}')
    print(f'剩料重量范围: {df["remaining_weight"].min():.1f} - {df["remaining_weight"].max():.1f} kg')
    print(f'剩料重量变异系数: {df["remaining_weight"].std()/df["remaining_weight"].mean()*100:.2f}%')
    print(f'时间差范围: {df["delta_T_minutes"].min():.1f} - {df["delta_T_minutes"].max():.1f} 分钟')
    
    # 分组分析
    print('\n按剩料重量分组的时间差分析:')
    df['weight_group'] = pd.cut(df['remaining_weight'], bins=4, labels=['低', '中低', '中高', '高'])
    group_stats = df.groupby('weight_group')['delta_T_minutes'].agg(['count', 'mean', 'std', 'median'])
    print(group_stats)
    
    # 异常值分析
    print('\n异常值分析:')
    outliers = df[df['any_outlier'] == True]
    print(f'总异常值数量: {len(outliers)}')
    if len(outliers) > 0:
        print('异常值详情:')
        for _, row in outliers.iterrows():
            print(f'  {row["source_dir"]}: 剩料重量={row["remaining_weight"]:.1f}kg, 时间差={row["delta_T_minutes"]:.1f}分钟')
    
    # 相关性详细分析
    print('\n相关性详细分析:')
    from scipy import stats
    
    x = df['remaining_weight']
    y = df['delta_T_minutes']
    
    # 移除异常值后的相关性
    df_no_outliers = df[df['any_outlier'] == False]
    x_clean = df_no_outliers['remaining_weight']
    y_clean = df_no_outliers['delta_T_minutes']
    
    pearson_all, p_all = stats.pearsonr(x, y)
    pearson_clean, p_clean = stats.pearsonr(x_clean, y_clean)
    
    print(f'全部数据相关系数: {pearson_all:.4f} (p={p_all:.4f})')
    print(f'移除异常值后相关系数: {pearson_clean:.4f} (p={p_clean:.4f})')
    
    print('\n结论:')
    print('• 剩料重量变异很小(变异系数<1%)，几乎为常数')
    print('• 时间差变异很大，主要受其他因素影响')
    print('• 两者之间无显著相关关系是合理的')
    print('• 剩料重量在工艺过程中保持相对稳定')

if __name__ == "__main__":
    detailed_summary()
