#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析CCD测温数据分布
"""

import pandas as pd
import numpy as np

def analyze_ccd_temperature():
    """
    分析CCD测温数据的分布
    """
    # 读取完整数据
    df = pd.read_csv('complete_auto_temp_data.csv')
    
    print("=" * 60)
    print("CCD测温数据分析")
    print("=" * 60)
    
    # 基本统计
    ccd_temps = df['CCD_temp_avg'].dropna()
    
    print(f"总样本数: {len(ccd_temps)}")
    print(f"最小值: {ccd_temps.min():.2f}°C")
    print(f"最大值: {ccd_temps.max():.2f}°C")
    print(f"平均值: {ccd_temps.mean():.2f}°C")
    print(f"中位数: {ccd_temps.median():.2f}°C")
    print(f"标准差: {ccd_temps.std():.2f}°C")
    
    # 分位数分析
    print(f"\n分位数分析:")
    print(f"5%分位数: {ccd_temps.quantile(0.05):.2f}°C")
    print(f"25%分位数: {ccd_temps.quantile(0.25):.2f}°C")
    print(f"75%分位数: {ccd_temps.quantile(0.75):.2f}°C")
    print(f"95%分位数: {ccd_temps.quantile(0.95):.2f}°C")
    
    # 温度范围分布
    print(f"\n温度范围分布:")
    ranges = [
        (0, 1400, "< 1400°C"),
        (1400, 1450, "1400-1450°C"),
        (1450, 1500, "1450-1500°C"),
        (1500, 1550, "1500-1550°C"),
        (1550, 1600, "1550-1600°C"),
        (1600, float('inf'), "> 1600°C")
    ]
    
    for min_temp, max_temp, label in ranges:
        if max_temp == float('inf'):
            count = len(ccd_temps[ccd_temps >= min_temp])
        else:
            count = len(ccd_temps[(ccd_temps >= min_temp) & (ccd_temps < max_temp)])
        percentage = count / len(ccd_temps) * 100
        print(f"{label}: {count} 条 ({percentage:.1f}%)")
    
    # 建议合理的温度范围
    print(f"\n建议的有效温度范围:")
    q5 = ccd_temps.quantile(0.05)
    q95 = ccd_temps.quantile(0.95)
    print(f"基于5%-95%分位数: {q5:.0f} - {q95:.0f}°C")
    
    mean = ccd_temps.mean()
    std = ccd_temps.std()
    print(f"基于均值±2标准差: {mean-2*std:.0f} - {mean+2*std:.0f}°C")
    
    # 显示一些具体数据
    print(f"\n前10个样本的CCD测温值:")
    for i, temp in enumerate(ccd_temps.head(10)):
        print(f"  样本{i+1}: {temp:.2f}°C")

if __name__ == "__main__":
    analyze_ccd_temperature()
