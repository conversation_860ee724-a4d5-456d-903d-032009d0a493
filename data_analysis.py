#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
永祥BC工段3s数据分析脚本
分析自动控温工序中副功率关闭到熔液占比达到100%的时间差
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 工序编码映射表
MAIN_PROCESS_MAP = {
    "待机": 0, "抽空": 1, "检漏": 2, "初始化": 3, "化料": 4,
    "压力化": 5, "稳温接触": 6, "引晶": 7, "放肩": 8, "转肩": 9,
    "等径": 10, "取段收尾": 11, "停炉": 12, "停炉收尾": 13, "快速收尾": 14,
    "运行": 15, "煅烧": 16, "自动控温": 17, "一键复投": 18, "自动定埚位": 19, "最后一桶加料": 20
}

AUXILIARY_PROCESS_MAP = {
    "副室净化": 1, "隔离旋开": 2, "投入料筒": 3, "提出料筒": 4, "籽晶预热": 5,
    "取段": 6, "断线提出": 7, "提肩": 8, "自动回熔": 9, "籽晶旋回": 10,
    "换籽晶": 11, "自动加料": 12, "料筒复投": 13, "主室旋闭": 14, "主室旋开": 15,
    "拆清": 16, "合炉盖": 17, "最后一桶料出": 18, "保压": 19
}

def decode_process_code(process_code):
    """
    解析工序编码
    工序编码 = 主工序序号 + (辅助工序序号 × 100)
    """
    if pd.isna(process_code) or process_code == 0:
        return None, None
    
    main_process_code = int(process_code) % 100
    auxiliary_process_code = int(process_code) // 100
    
    # 查找主工序名称
    main_process_name = None
    for name, code in MAIN_PROCESS_MAP.items():
        if code == main_process_code:
            main_process_name = name
            break
    
    # 查找辅助工序名称
    auxiliary_process_name = None
    if auxiliary_process_code > 0:
        for name, code in AUXILIARY_PROCESS_MAP.items():
            if code == auxiliary_process_code:
                auxiliary_process_name = name
                break
    
    return main_process_name, auxiliary_process_name

def load_csv_files(base_path, max_dirs=3, max_files_per_dir=2):
    """
    加载CSV文件（限制数量以便快速测试）
    """
    all_data = []

    # 获取所有子目录
    subdirs = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]

    print(f"找到 {len(subdirs)} 个数据目录，将处理前 {max_dirs} 个目录")

    for i, subdir in enumerate(subdirs[:max_dirs]):
        subdir_path = os.path.join(base_path, subdir)
        csv_files = glob.glob(os.path.join(subdir_path, "*.csv"))

        print(f"处理目录: {subdir}, 包含 {len(csv_files)} 个CSV文件，将处理前 {max_files_per_dir} 个文件")

        for j, csv_file in enumerate(csv_files[:max_files_per_dir]):
            try:
                # 读取CSV文件
                df = pd.read_csv(csv_file, encoding='utf-8')
                df['source_file'] = csv_file
                df['source_dir'] = subdir
                all_data.append(df)
                print(f"  成功加载: {os.path.basename(csv_file)}, 记录数: {len(df)}")
            except Exception as e:
                print(f"  加载失败: {csv_file}, 错误: {e}")

    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"\n总计加载记录数: {len(combined_df)}")
        return combined_df
    else:
        print("未能加载任何数据")
        return None

def analyze_auto_temp_control(df):
    """
    分析自动控温工序的数据
    """
    print("\n开始分析自动控温工序...")
    
    # 解析工序编码
    df['主工序'], df['辅助工序'] = zip(*df['工序'].apply(decode_process_code))
    
    # 筛选自动控温工序数据
    auto_temp_data = df[df['主工序'] == '自动控温'].copy()
    print(f"找到自动控温工序记录数: {len(auto_temp_data)}")
    
    if len(auto_temp_data) == 0:
        print("未找到自动控温工序数据")
        return None
    
    # 转换时间字段
    auto_temp_data['datetime'] = pd.to_datetime(auto_temp_data['time'])
    auto_temp_data = auto_temp_data.sort_values('datetime')
    
    # 按源文件分组分析（每个文件可能代表一个工序周期）
    results = []
    
    for source_file in auto_temp_data['source_file'].unique():
        file_data = auto_temp_data[auto_temp_data['source_file'] == source_file].copy()
        file_data = file_data.sort_values('datetime')
        
        print(f"\n分析文件: {os.path.basename(source_file)}")
        print(f"  记录数: {len(file_data)}")
        
        # 找到副功率为0的时间点
        zero_power_data = file_data[file_data['副功率'] == 0]
        
        if len(zero_power_data) == 0:
            print("  未找到副功率为0的记录")
            continue
        
        # 取第一个副功率为0的时间点作为T1
        t1 = zero_power_data.iloc[0]['datetime']
        print(f"  T1 (副功率首次为0): {t1}")
        
        # 从T1开始查找熔液占比首次达到100的时间点
        after_t1_data = file_data[file_data['datetime'] >= t1]
        liquid_100_data = after_t1_data[after_t1_data['熔液占比'] >= 100]
        
        if len(liquid_100_data) == 0:
            print("  未找到熔液占比达到100%的记录")
            continue
        
        # 取第一个熔液占比达到100的时间点作为T2
        t2 = liquid_100_data.iloc[0]['datetime']
        print(f"  T2 (熔液占比首次达到100%): {t2}")
        
        # 计算时间差
        delta_t = (t2 - t1).total_seconds() / 60  # 转换为分钟
        print(f"  ΔT (时间差): {delta_t:.2f} 分钟")
        
        results.append({
            'source_file': source_file,
            'source_dir': file_data.iloc[0]['source_dir'],
            'T1': t1,
            'T2': t2,
            'delta_T_minutes': delta_t,
            'records_count': len(file_data)
        })
    
    return results

def generate_statistics(results):
    """
    生成统计分析结果
    """
    if not results:
        print("没有有效的分析结果")
        return
    
    delta_times = [r['delta_T_minutes'] for r in results]
    
    print(f"\n=== 统计分析结果 ===")
    print(f"有效工序数量: {len(results)}")
    print(f"平均时间差: {np.mean(delta_times):.2f} 分钟")
    print(f"最大时间差: {np.max(delta_times):.2f} 分钟")
    print(f"最小时间差: {np.min(delta_times):.2f} 分钟")
    print(f"标准差: {np.std(delta_times):.2f} 分钟")
    print(f"中位数: {np.median(delta_times):.2f} 分钟")
    
    print(f"\n=== 详细记录 ===")
    for i, result in enumerate(results, 1):
        print(f"{i:2d}. 目录: {result['source_dir']}")
        print(f"     文件: {os.path.basename(result['source_file'])}")
        print(f"     T1: {result['T1']}")
        print(f"     T2: {result['T2']}")
        print(f"     ΔT: {result['delta_T_minutes']:.2f} 分钟")
        print(f"     记录数: {result['records_count']}")
        print()

def main():
    """
    主函数
    """
    # 数据路径
    base_path = r"\\10.0.10.249\root\FreeSpace\B.Jx_BigData\1.Data\永祥BC工段3s数据"
    
    print("开始加载数据...")
    
    # 加载数据
    df = load_csv_files(base_path)
    
    if df is None:
        print("数据加载失败，程序退出")
        return
    
    # 分析自动控温工序
    results = analyze_auto_temp_control(df)
    
    # 生成统计结果
    generate_statistics(results)

if __name__ == "__main__":
    main()
