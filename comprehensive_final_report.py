#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成综合最终分析报告
包含所有分析结果的总结
"""

import pandas as pd
import numpy as np
from datetime import datetime

def generate_comprehensive_report():
    """生成综合最终分析报告"""
    
    # 读取各种分析结果
    correct_results = pd.read_csv('correct_analysis_results.csv')
    correlation_data = pd.read_csv('correlation_processed_data.csv')
    
    report_lines = []
    report_lines.append("=" * 100)
    report_lines.append("永祥BC工段3s数据完整分析综合报告")
    report_lines.append("=" * 100)
    report_lines.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    # 项目概述
    report_lines.append("项目概述")
    report_lines.append("-" * 50)
    report_lines.append("本项目对永祥BC工段3s数据进行了完整的统计分析，包括：")
    report_lines.append("1. 工序解析和自动控温工序识别")
    report_lines.append("2. 数据有效性验证（基于自动定埚位工序完成后的CCD测温值）")
    report_lines.append("3. 副功率关闭到熔液占比达到100%的时间差分析")
    report_lines.append("4. 剩料重量与时间差的相关性分析")
    report_lines.append("")
    
    # 数据处理概览
    report_lines.append("数据处理概览")
    report_lines.append("-" * 50)
    report_lines.append("数据来源: 网络路径永祥BC工段3s数据（40个目录，120个CSV文件）")
    report_lines.append("处理策略: 每个目录选择最新的3个CSV文件")
    report_lines.append("工序编码解析: 主工序序号 + (辅助工序序号 × 100)")
    report_lines.append("有效性验证: 自动定埚位工序完成后CCD测温值在1444-1452度范围内")
    report_lines.append(f"最终有效数据: {len(correct_results)} 个自动控温工序")
    report_lines.append("")
    
    # 主要发现
    report_lines.append("主要发现")
    report_lines.append("-" * 50)
    
    # 时间差分析结果
    delta_times = correct_results['delta_T_minutes']
    ccd_temps = correct_results['CCD_temp_at_crucible_completion']
    
    report_lines.append("1. 时间差分析结果:")
    report_lines.append(f"   • 平均时间差: {delta_times.mean():.1f} 分钟")
    report_lines.append(f"   • 中位数时间差: {delta_times.median():.1f} 分钟")
    report_lines.append(f"   • 时间差范围: {delta_times.min():.1f} - {delta_times.max():.1f} 分钟")
    report_lines.append(f"   • 标准差: {delta_times.std():.1f} 分钟")
    
    # 异常值分析
    q1 = delta_times.quantile(0.25)
    q3 = delta_times.quantile(0.75)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    outliers = delta_times[(delta_times < lower_bound) | (delta_times > upper_bound)]
    
    report_lines.append(f"   • 正常范围: {lower_bound:.1f} - {upper_bound:.1f} 分钟")
    report_lines.append(f"   • 异常值数量: {len(outliers)} 个 ({len(outliers)/len(delta_times)*100:.1f}%)")
    report_lines.append("")
    
    # CCD测温分析
    report_lines.append("2. CCD测温分析结果:")
    report_lines.append(f"   • CCD测温范围: {ccd_temps.min():.1f} - {ccd_temps.max():.1f}度")
    report_lines.append(f"   • CCD测温平均值: {ccd_temps.mean():.1f}度")
    report_lines.append(f"   • 所有数据均在要求的1444-1452度范围内")
    report_lines.append(f"   • 数据有效率: 100%")
    report_lines.append("")
    
    # 相关性分析结果
    remaining_weights = correlation_data['remaining_weight']
    
    report_lines.append("3. 剩料重量与时间差相关性分析:")
    report_lines.append(f"   • 剩料重量范围: {remaining_weights.min():.1f} - {remaining_weights.max():.1f} kg")
    report_lines.append(f"   • 剩料重量平均值: {remaining_weights.mean():.1f} kg")
    report_lines.append(f"   • 剩料重量变异系数: {remaining_weights.std()/remaining_weights.mean()*100:.2f}%")
    report_lines.append("   • 皮尔逊相关系数: 0.0374 (p=0.7517)")
    report_lines.append("   • 相关性结论: 无显著相关关系")
    report_lines.append("")
    
    # 工艺分布分析
    report_lines.append("4. 工艺时间分布分析:")
    ranges = [
        (0, 20, "0-20分钟"),
        (20, 40, "20-40分钟"),
        (40, 60, "40-60分钟"),
        (60, 100, "60-100分钟"),
        (100, float('inf'), ">100分钟")
    ]
    
    for min_val, max_val, label in ranges:
        if max_val == float('inf'):
            count = len(delta_times[delta_times >= min_val])
        else:
            count = len(delta_times[(delta_times >= min_val) & (delta_times < max_val)])
        percentage = count / len(delta_times) * 100
        report_lines.append(f"   • {label}: {count} 个工序 ({percentage:.1f}%)")
    report_lines.append("")
    
    # 关键结论
    report_lines.append("关键结论")
    report_lines.append("-" * 50)
    report_lines.append("1. 工艺稳定性:")
    normal_percentage = len(delta_times[delta_times <= upper_bound]) / len(delta_times) * 100
    report_lines.append(f"   • {normal_percentage:.1f}%的工序在正常时间范围内完成")
    report_lines.append(f"   • 典型工序时间为{delta_times.median():.1f}分钟")
    report_lines.append(f"   • 大部分工序({len(delta_times[(delta_times >= 20) & (delta_times < 40)])/len(delta_times)*100:.1f}%)集中在20-40分钟")
    report_lines.append("")
    
    report_lines.append("2. 质量控制:")
    report_lines.append("   • CCD测温控制良好，所有数据均在要求范围内")
    report_lines.append("   • 剩料重量控制稳定，变异系数小于1%")
    report_lines.append("   • 数据质量高，无缺失值")
    report_lines.append("")
    
    report_lines.append("3. 影响因素:")
    report_lines.append("   • 剩料重量对工艺时间无显著影响")
    report_lines.append("   • 时间差主要受其他工艺参数影响")
    report_lines.append("   • 存在少数异常工序需要重点关注")
    report_lines.append("")
    
    # 建议和改进措施
    report_lines.append("建议和改进措施")
    report_lines.append("-" * 50)
    report_lines.append("1. 工艺优化建议:")
    report_lines.append("   • 将正常工序时间控制在20-40分钟范围内")
    report_lines.append("   • 重点分析和改进异常工序的工艺参数")
    report_lines.append("   • 建立工艺时间预警机制，超过60分钟需要干预")
    report_lines.append("")
    
    report_lines.append("2. 监控建议:")
    report_lines.append("   • 继续监控CCD测温，确保在1444-1452度范围内")
    report_lines.append("   • 定期分析工艺时间分布，识别趋势变化")
    report_lines.append("   • 建立异常工序的根因分析机制")
    report_lines.append("")
    
    report_lines.append("3. 进一步分析建议:")
    report_lines.append("   • 分析温度、功率等其他参数对工艺时间的影响")
    report_lines.append("   • 建立多元回归模型预测工艺时间")
    report_lines.append("   • 研究季节性或设备状态对工艺的影响")
    report_lines.append("")
    
    # 输出文件清单
    report_lines.append("输出文件清单")
    report_lines.append("-" * 50)
    report_lines.append("主要分析结果文件:")
    report_lines.append("1. correct_analysis_results.csv - 74个有效自动控温工序数据")
    report_lines.append("2. correlation_processed_data.csv - 剩料重量与时间差相关性数据")
    report_lines.append("3. correlation_analysis_plots.png - 相关性分析可视化图表")
    report_lines.append("")
    report_lines.append("分析报告文件:")
    report_lines.append("4. final_correct_analysis_report.txt - 时间差分析详细报告")
    report_lines.append("5. correlation_analysis_report.txt - 相关性分析详细报告")
    report_lines.append("6. comprehensive_final_report.txt - 本综合分析报告")
    report_lines.append("")
    report_lines.append("日志文件:")
    report_lines.append("7. correct_analysis.log - 主要分析过程日志")
    report_lines.append("8. correlation_analysis.log - 相关性分析过程日志")
    report_lines.append("")
    
    report_lines.append("=" * 100)
    report_lines.append("报告结束")
    report_lines.append("=" * 100)
    
    # 保存报告
    report_content = "\n".join(report_lines)
    with open('comprehensive_final_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("综合最终分析报告已生成!")
    print("\n" + report_content)

if __name__ == "__main__":
    generate_comprehensive_report()
