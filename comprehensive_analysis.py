#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
永祥BC工段3s数据完整分析脚本
按照要求进行完整的数据处理和统计分析
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import warnings
import logging
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 工序编码映射表
MAIN_PROCESS_MAP = {
    "待机": 0, "抽空": 1, "检漏": 2, "初始化": 3, "化料": 4,
    "压力化": 5, "稳温接触": 6, "引晶": 7, "放肩": 8, "转肩": 9,
    "等径": 10, "取段收尾": 11, "停炉": 12, "停炉收尾": 13, "快速收尾": 14,
    "运行": 15, "煅烧": 16, "自动控温": 17, "一键复投": 18, "自动定埚位": 19, "最后一桶加料": 20
}

AUXILIARY_PROCESS_MAP = {
    "副室净化": 1, "隔离旋开": 2, "投入料筒": 3, "提出料筒": 4, "籽晶预热": 5,
    "取段": 6, "断线提出": 7, "提肩": 8, "自动回熔": 9, "籽晶旋回": 10,
    "换籽晶": 11, "自动加料": 12, "料筒复投": 13, "主室旋闭": 14, "主室旋开": 15,
    "拆清": 16, "合炉盖": 17, "最后一桶料出": 18, "保压": 19
}

def decode_process_code(process_code):
    """
    解析工序编码
    工序编码 = 主工序序号 + (辅助工序序号 × 100)
    """
    if pd.isna(process_code) or process_code == 0:
        return None, None
    
    try:
        process_code = int(float(process_code))
        main_process_code = process_code % 100
        auxiliary_process_code = process_code // 100
        
        # 查找主工序名称
        main_process_name = None
        for name, code in MAIN_PROCESS_MAP.items():
            if code == main_process_code:
                main_process_name = name
                break
        
        # 查找辅助工序名称
        auxiliary_process_name = None
        if auxiliary_process_code > 0:
            for name, code in AUXILIARY_PROCESS_MAP.items():
                if code == auxiliary_process_code:
                    auxiliary_process_name = name
                    break
        
        return main_process_name, auxiliary_process_name
    except (ValueError, TypeError):
        return None, None

def extract_date_from_filename(filename):
    """
    从文件名中提取结束日期
    文件名格式：export_YYYYMMDD_YYYYMMDD.csv
    返回结束日期（第二个日期）用于排序
    """
    import re

    # 使用正则表达式提取日期
    pattern = r'export_(\d{8})_(\d{8})\.csv'
    match = re.search(pattern, filename)

    if match:
        start_date = match.group(1)
        end_date = match.group(2)
        return end_date
    else:
        # 如果无法解析，返回文件名用于排序
        return filename

def select_latest_files(csv_files, max_files=3):
    """
    选择每个目录中最新的CSV文件
    按照文件名中的结束日期进行降序排序，选择最新的max_files个文件
    """
    if not csv_files:
        return []

    # 创建文件信息列表，包含文件路径和提取的日期
    file_info = []
    for csv_file in csv_files:
        filename = os.path.basename(csv_file)
        end_date = extract_date_from_filename(filename)
        file_info.append((csv_file, end_date, filename))

    # 按结束日期降序排序
    file_info.sort(key=lambda x: x[1], reverse=True)

    # 选择最新的max_files个文件
    selected_files = [info[0] for info in file_info[:max_files]]
    selected_filenames = [info[2] for info in file_info[:max_files]]

    return selected_files, selected_filenames

def load_all_csv_files(base_path, max_files_per_dir=3):
    """
    加载CSV文件（优化版本：每个目录只处理最新的几个文件）
    """
    all_data = []

    # 获取所有子目录
    subdirs = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]

    logger.info(f"找到 {len(subdirs)} 个数据目录")
    logger.info(f"每个目录将处理最新的 {max_files_per_dir} 个CSV文件")

    total_files = 0
    successful_files = 0

    for subdir in subdirs:
        subdir_path = os.path.join(base_path, subdir)
        all_csv_files = glob.glob(os.path.join(subdir_path, "*.csv"))

        # 选择最新的文件
        selected_files, selected_filenames = select_latest_files(all_csv_files, max_files_per_dir)

        logger.info(f"处理目录: {subdir}")
        logger.info(f"  总CSV文件数: {len(all_csv_files)}")
        logger.info(f"  选择处理文件数: {len(selected_files)}")
        logger.info(f"  选择的文件: {', '.join(selected_filenames)}")

        total_files += len(selected_files)

        for csv_file in selected_files:
            try:
                # 读取CSV文件
                df = pd.read_csv(csv_file, encoding='utf-8')
                df['source_file'] = csv_file
                df['source_dir'] = subdir
                all_data.append(df)
                successful_files += 1

                logger.info(f"    成功加载: {os.path.basename(csv_file)}, 记录数: {len(df)}")

            except Exception as e:
                logger.error(f"    加载失败: {csv_file}, 错误: {e}")

    logger.info(f"总计成功加载 {successful_files}/{total_files} 个文件")

    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        logger.info(f"总计加载记录数: {len(combined_df)}")
        return combined_df
    else:
        logger.error("未能加载任何数据")
        return None

def analyze_auto_temp_control_complete(df):
    """
    完整分析自动控温工序的数据
    """
    logger.info("开始分析自动控温工序...")
    
    # 解析工序编码
    logger.info("解析工序编码...")
    df['主工序'], df['辅助工序'] = zip(*df['工序'].apply(decode_process_code))
    
    # 筛选自动控温工序数据
    auto_temp_data = df[df['主工序'] == '自动控温'].copy()
    logger.info(f"找到自动控温工序记录数: {len(auto_temp_data)}")
    
    if len(auto_temp_data) == 0:
        logger.warning("未找到自动控温工序数据")
        return []
    
    # 转换时间字段
    auto_temp_data['datetime'] = pd.to_datetime(auto_temp_data['time'])
    auto_temp_data = auto_temp_data.sort_values('datetime')
    
    # 按源文件分组分析（每个文件可能代表一个工序周期）
    results = []
    
    unique_files = auto_temp_data['source_file'].unique()
    logger.info(f"需要分析 {len(unique_files)} 个包含自动控温工序的文件")
    
    for i, source_file in enumerate(unique_files):
        if i % 10 == 0:
            logger.info(f"分析进度: {i+1}/{len(unique_files)}")
            
        file_data = auto_temp_data[auto_temp_data['source_file'] == source_file].copy()
        file_data = file_data.sort_values('datetime')
        
        # 找到副功率为0的时间点
        zero_power_data = file_data[file_data['副功率'] == 0]
        
        if len(zero_power_data) == 0:
            logger.debug(f"文件 {os.path.basename(source_file)} 未找到副功率为0的记录")
            continue
        
        # 取第一个副功率为0的时间点作为T1
        t1_record = zero_power_data.iloc[0]
        t1 = t1_record['datetime']
        
        # 从T1开始查找熔液占比首次达到100的时间点
        after_t1_data = file_data[file_data['datetime'] >= t1]
        liquid_100_data = after_t1_data[after_t1_data['熔液占比'] >= 100]
        
        if len(liquid_100_data) == 0:
            logger.debug(f"文件 {os.path.basename(source_file)} 未找到熔液占比达到100%的记录")
            continue
        
        # 取第一个熔液占比达到100的时间点作为T2
        t2_record = liquid_100_data.iloc[0]
        t2 = t2_record['datetime']
        
        # 计算时间差
        delta_t = (t2 - t1).total_seconds() / 60  # 转换为分钟
        
        # 获取T1到T2时间段内的CCD测温代表值（平均值）
        t1_t2_data = file_data[(file_data['datetime'] >= t1) & (file_data['datetime'] <= t2)]
        ccd_temp_values = t1_t2_data['CCD测温'].dropna()
        ccd_temp_avg = ccd_temp_values.mean() if len(ccd_temp_values) > 0 else np.nan
        
        results.append({
            'source_dir': file_data.iloc[0]['source_dir'],
            'source_file': os.path.basename(source_file),
            'T1': t1,
            'T2': t2,
            'delta_T_minutes': delta_t,
            'CCD_temp_avg': ccd_temp_avg,
            'records_count': len(file_data),
            'valid_data': True  # 初始标记为有效，后续会根据CCD温度范围进行筛选
        })
    
    logger.info(f"完成分析，找到 {len(results)} 个有效的自动控温工序")
    return results

def filter_valid_data(results):
    """
    第二步：数据有效性筛选
    有效数据标准：CCD测温值在1444-1452度范围内
    """
    logger.info("开始数据有效性筛选...")

    if not results:
        logger.warning("没有数据需要筛选")
        return []

    # 转换为DataFrame便于处理
    df_results = pd.DataFrame(results)

    # 应用CCD温度范围筛选（基于实际数据分布调整）
    # 使用5%-95%分位数作为有效范围：1516-1553°C
    temp_min, temp_max = 1516, 1553

    # 标记有效数据
    df_results['valid_data'] = (
        (df_results['CCD_temp_avg'] >= temp_min) &
        (df_results['CCD_temp_avg'] <= temp_max) &
        (~df_results['CCD_temp_avg'].isna())
    )

    # 添加无效原因
    df_results['invalid_reason'] = ''
    df_results.loc[df_results['CCD_temp_avg'].isna(), 'invalid_reason'] = 'CCD测温数据缺失'
    df_results.loc[
        (~df_results['CCD_temp_avg'].isna()) &
        (df_results['CCD_temp_avg'] < temp_min),
        'invalid_reason'
    ] = f'CCD测温低于{temp_min}度'
    df_results.loc[
        (~df_results['CCD_temp_avg'].isna()) &
        (df_results['CCD_temp_avg'] > temp_max),
        'invalid_reason'
    ] = f'CCD测温高于{temp_max}度'

    total_count = len(df_results)
    valid_count = df_results['valid_data'].sum()
    invalid_count = total_count - valid_count

    logger.info(f"数据有效性筛选结果:")
    logger.info(f"  总数据量: {total_count}")
    logger.info(f"  有效数据量: {valid_count}")
    logger.info(f"  无效数据量: {invalid_count}")

    if invalid_count > 0:
        logger.info("无效数据原因统计:")
        invalid_reasons = df_results[~df_results['valid_data']]['invalid_reason'].value_counts()
        for reason, count in invalid_reasons.items():
            logger.info(f"  {reason}: {count} 条")

    return df_results.to_dict('records')

def generate_statistics(results):
    """
    第三步：统计分析
    """
    logger.info("开始统计分析...")

    if not results:
        logger.warning("没有数据进行统计分析")
        return {}

    df_results = pd.DataFrame(results)

    # 分别统计全部数据和有效数据
    all_delta_times = df_results['delta_T_minutes'].dropna()
    valid_data = df_results[df_results['valid_data'] == True]
    valid_delta_times = valid_data['delta_T_minutes'].dropna()

    stats = {
        'total_records': len(df_results),
        'valid_records': len(valid_data),
        'invalid_records': len(df_results) - len(valid_data),
        'all_data_stats': {},
        'valid_data_stats': {}
    }

    # 全部数据统计
    if len(all_delta_times) > 0:
        stats['all_data_stats'] = {
            'count': len(all_delta_times),
            'mean': float(all_delta_times.mean()),
            'std': float(all_delta_times.std()),
            'min': float(all_delta_times.min()),
            'max': float(all_delta_times.max()),
            'median': float(all_delta_times.median()),
            'q25': float(all_delta_times.quantile(0.25)),
            'q75': float(all_delta_times.quantile(0.75))
        }

    # 有效数据统计
    if len(valid_delta_times) > 0:
        stats['valid_data_stats'] = {
            'count': len(valid_delta_times),
            'mean': float(valid_delta_times.mean()),
            'std': float(valid_delta_times.std()),
            'min': float(valid_delta_times.min()),
            'max': float(valid_delta_times.max()),
            'median': float(valid_delta_times.median()),
            'q25': float(valid_delta_times.quantile(0.25)),
            'q75': float(valid_delta_times.quantile(0.75))
        }

    # 异常值分析（使用IQR方法）
    if len(valid_delta_times) > 0:
        q1 = valid_delta_times.quantile(0.25)
        q3 = valid_delta_times.quantile(0.75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        outliers = valid_delta_times[(valid_delta_times < lower_bound) | (valid_delta_times > upper_bound)]
        stats['outlier_analysis'] = {
            'outlier_count': len(outliers),
            'outlier_percentage': len(outliers) / len(valid_delta_times) * 100,
            'lower_bound': float(lower_bound),
            'upper_bound': float(upper_bound)
        }

    return stats

def save_results_to_csv(results, filename):
    """
    保存结果到CSV文件
    """
    if not results:
        logger.warning(f"没有数据保存到 {filename}")
        return

    df = pd.DataFrame(results)
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    logger.info(f"结果已保存到: {filename}")

def generate_report(stats, results):
    """
    生成详细的数据分析报告
    """
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("永祥BC工段3s数据分析报告")
    report_lines.append("=" * 80)
    report_lines.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")

    # 数据概览
    report_lines.append("1. 数据概览")
    report_lines.append("-" * 40)
    report_lines.append(f"总数据量: {stats['total_records']} 个自动控温工序")
    report_lines.append(f"有效数据量: {stats['valid_records']} 个")
    report_lines.append(f"无效数据量: {stats['invalid_records']} 个")
    report_lines.append(f"数据有效率: {stats['valid_records']/stats['total_records']*100:.2f}%")
    report_lines.append("")

    # 全部数据统计
    if stats['all_data_stats']:
        report_lines.append("2. 全部数据统计（时间差ΔT，单位：分钟）")
        report_lines.append("-" * 40)
        all_stats = stats['all_data_stats']
        report_lines.append(f"样本数量: {all_stats['count']}")
        report_lines.append(f"平均值: {all_stats['mean']:.2f}")
        report_lines.append(f"标准差: {all_stats['std']:.2f}")
        report_lines.append(f"最小值: {all_stats['min']:.2f}")
        report_lines.append(f"最大值: {all_stats['max']:.2f}")
        report_lines.append(f"中位数: {all_stats['median']:.2f}")
        report_lines.append(f"第一四分位数(Q1): {all_stats['q25']:.2f}")
        report_lines.append(f"第三四分位数(Q3): {all_stats['q75']:.2f}")
        report_lines.append("")

    # 有效数据统计
    if stats['valid_data_stats']:
        report_lines.append("3. 有效数据统计（时间差ΔT，单位：分钟）")
        report_lines.append("-" * 40)
        valid_stats = stats['valid_data_stats']
        report_lines.append(f"样本数量: {valid_stats['count']}")
        report_lines.append(f"平均值: {valid_stats['mean']:.2f}")
        report_lines.append(f"标准差: {valid_stats['std']:.2f}")
        report_lines.append(f"最小值: {valid_stats['min']:.2f}")
        report_lines.append(f"最大值: {valid_stats['max']:.2f}")
        report_lines.append(f"中位数: {valid_stats['median']:.2f}")
        report_lines.append(f"第一四分位数(Q1): {valid_stats['q25']:.2f}")
        report_lines.append(f"第三四分位数(Q3): {valid_stats['q75']:.2f}")
        report_lines.append("")

    # 异常值分析
    if 'outlier_analysis' in stats:
        report_lines.append("4. 异常值分析")
        report_lines.append("-" * 40)
        outlier = stats['outlier_analysis']
        report_lines.append(f"异常值数量: {outlier['outlier_count']}")
        report_lines.append(f"异常值比例: {outlier['outlier_percentage']:.2f}%")
        report_lines.append(f"正常值范围: [{outlier['lower_bound']:.2f}, {outlier['upper_bound']:.2f}] 分钟")
        report_lines.append("")

    # 无效数据分析
    if results:
        df_results = pd.DataFrame(results)
        invalid_data = df_results[df_results['valid_data'] == False]
        if len(invalid_data) > 0:
            report_lines.append("5. 无效数据原因分析")
            report_lines.append("-" * 40)
            invalid_reasons = invalid_data['invalid_reason'].value_counts()
            for reason, count in invalid_reasons.items():
                percentage = count / len(df_results) * 100
                report_lines.append(f"{reason}: {count} 条 ({percentage:.2f}%)")
            report_lines.append("")

    report_lines.append("=" * 80)

    # 保存报告
    report_content = "\n".join(report_lines)
    with open('data_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)

    logger.info("详细分析报告已保存到: data_analysis_report.txt")

    # 同时输出到控制台
    print("\n" + report_content)

def main():
    """
    主函数：执行完整的数据分析流程
    """
    logger.info("开始永祥BC工段3s数据完整分析")

    # 数据路径
    base_path = r"\\10.0.10.249\root\FreeSpace\B.Jx_BigData\1.Data\永祥BC工段3s数据"

    try:
        # 第一步：完整数据统计
        logger.info("=" * 60)
        logger.info("第一步：完整数据统计")
        logger.info("=" * 60)

        # 加载所有数据
        df = load_all_csv_files(base_path)

        if df is None:
            logger.error("数据加载失败，程序退出")
            return

        # 分析自动控温工序
        results = analyze_auto_temp_control_complete(df)

        if not results:
            logger.error("未找到有效的自动控温工序数据，程序退出")
            return

        # 保存完整数据统计结果
        save_results_to_csv(results, 'complete_auto_temp_data.csv')

        # 第二步：数据有效性筛选
        logger.info("=" * 60)
        logger.info("第二步：数据有效性筛选")
        logger.info("=" * 60)

        # 应用有效性筛选
        filtered_results = filter_valid_data(results)

        # 保存有效数据
        valid_results = [r for r in filtered_results if r['valid_data']]
        save_results_to_csv(valid_results, 'valid_auto_temp_data.csv')

        # 第三步：统计分析
        logger.info("=" * 60)
        logger.info("第三步：统计分析")
        logger.info("=" * 60)

        # 生成统计分析
        stats = generate_statistics(filtered_results)

        # 生成详细报告
        generate_report(stats, filtered_results)

        logger.info("=" * 60)
        logger.info("数据分析完成！")
        logger.info("=" * 60)
        logger.info("输出文件:")
        logger.info("1. complete_auto_temp_data.csv - 完整数据统计")
        logger.info("2. valid_auto_temp_data.csv - 有效数据")
        logger.info("3. data_analysis_report.txt - 详细分析报告")
        logger.info("4. data_analysis.log - 处理日志")

    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()
