#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的永祥BC工段3s数据分析脚本
按照正确的需求进行分析：
1. 先检查自动定埚位工序完成后的CCD测温值(1444-1452度)
2. 只对有效数据进行自动控温工序的时间差分析
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import warnings
import logging
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('correct_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 工序编码映射表
MAIN_PROCESS_MAP = {
    "待机": 0, "抽空": 1, "检漏": 2, "初始化": 3, "化料": 4,
    "压力化": 5, "稳温接触": 6, "引晶": 7, "放肩": 8, "转肩": 9,
    "等径": 10, "取段收尾": 11, "停炉": 12, "停炉收尾": 13, "快速收尾": 14,
    "运行": 15, "煅烧": 16, "自动控温": 17, "一键复投": 18, "自动定埚位": 19, "最后一桶加料": 20
}

def decode_process_code(process_code):
    """解析工序编码"""
    if pd.isna(process_code) or process_code == 0:
        return None, None
    
    try:
        process_code = int(float(process_code))
        main_process_code = process_code % 100
        auxiliary_process_code = process_code // 100
        
        # 查找主工序名称
        main_process_name = None
        for name, code in MAIN_PROCESS_MAP.items():
            if code == main_process_code:
                main_process_name = name
                break
        
        return main_process_name, auxiliary_process_code
    except (ValueError, TypeError):
        return None, None

def load_and_analyze_files(base_path, max_files_per_dir=3):
    """
    加载文件并进行正确的分析
    """
    all_results = []
    
    # 获取所有子目录
    subdirs = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]
    logger.info(f"找到 {len(subdirs)} 个数据目录")
    
    for subdir in subdirs:
        subdir_path = os.path.join(base_path, subdir)
        all_csv_files = glob.glob(os.path.join(subdir_path, "*.csv"))
        
        # 选择最新的文件
        selected_files = select_latest_files(all_csv_files, max_files_per_dir)
        
        logger.info(f"处理目录: {subdir}, 选择 {len(selected_files)} 个文件")
        
        for csv_file in selected_files:
            try:
                # 分析单个文件
                file_results = analyze_single_file(csv_file, subdir)
                all_results.extend(file_results)
                
            except Exception as e:
                logger.error(f"处理文件失败: {csv_file}, 错误: {e}")
    
    return all_results

def select_latest_files(csv_files, max_files=3):
    """选择最新的CSV文件"""
    if not csv_files:
        return []
    
    import re
    file_info = []
    for csv_file in csv_files:
        filename = os.path.basename(csv_file)
        pattern = r'export_(\d{8})_(\d{8})\.csv'
        match = re.search(pattern, filename)
        
        if match:
            end_date = match.group(2)
            file_info.append((csv_file, end_date))
        else:
            file_info.append((csv_file, filename))
    
    # 按结束日期降序排序
    file_info.sort(key=lambda x: x[1], reverse=True)
    return [info[0] for info in file_info[:max_files]]

def analyze_single_file(csv_file, source_dir):
    """
    分析单个文件，按照正确的流程
    """
    results = []
    
    try:
        # 读取文件
        df = pd.read_csv(csv_file, encoding='utf-8')
        df['datetime'] = pd.to_datetime(df['time'])
        df = df.sort_values('datetime')
        
        # 解析工序编码
        df['主工序'], df['辅助工序'] = zip(*df['工序'].apply(decode_process_code))
        
        # 第一步：检查自动定埚位工序完成后的CCD测温
        auto_crucible_data = df[df['主工序'] == '自动定埚位']
        
        if len(auto_crucible_data) == 0:
            logger.debug(f"文件 {os.path.basename(csv_file)} 未找到自动定埚位工序")
            return results
        
        # 获取自动定埚位工序完成时的CCD测温值
        # 取自动定埚位工序的最后一个记录作为完成时间点
        crucible_completion = auto_crucible_data.iloc[-1]
        ccd_temp_at_completion = crucible_completion['CCD测温']
        
        # 检查CCD测温是否在有效范围内(1444-1452度)
        temp_min, temp_max = 1444, 1452
        is_valid_temp = (ccd_temp_at_completion >= temp_min and 
                        ccd_temp_at_completion <= temp_max and 
                        not pd.isna(ccd_temp_at_completion))
        
        if not is_valid_temp:
            logger.debug(f"文件 {os.path.basename(csv_file)} CCD测温不在有效范围: {ccd_temp_at_completion}")
            return results
        
        # 第二步：对有效数据分析自动控温工序
        auto_temp_data = df[df['主工序'] == '自动控温']
        
        if len(auto_temp_data) == 0:
            logger.debug(f"文件 {os.path.basename(csv_file)} 未找到自动控温工序")
            return results
        
        auto_temp_data = auto_temp_data.sort_values('datetime')
        
        # 找到副功率为0的时间点
        zero_power_data = auto_temp_data[auto_temp_data['副功率'] == 0]
        
        if len(zero_power_data) == 0:
            logger.debug(f"文件 {os.path.basename(csv_file)} 未找到副功率为0的记录")
            return results
        
        # 取第一个副功率为0的时间点作为T1
        t1 = zero_power_data.iloc[0]['datetime']
        
        # 从T1开始查找熔液占比首次达到100的时间点
        after_t1_data = auto_temp_data[auto_temp_data['datetime'] >= t1]
        liquid_100_data = after_t1_data[after_t1_data['熔液占比'] >= 100]
        
        if len(liquid_100_data) == 0:
            logger.debug(f"文件 {os.path.basename(csv_file)} 未找到熔液占比达到100%的记录")
            return results
        
        # 取第一个熔液占比达到100的时间点作为T2
        t2 = liquid_100_data.iloc[0]['datetime']
        
        # 计算时间差
        delta_t = (t2 - t1).total_seconds() / 60  # 转换为分钟
        
        results.append({
            'source_dir': source_dir,
            'source_file': os.path.basename(csv_file),
            'T1': t1,
            'T2': t2,
            'delta_T_minutes': delta_t,
            'CCD_temp_at_crucible_completion': ccd_temp_at_completion,
            'crucible_completion_time': crucible_completion['datetime'],
            'records_count': len(df),
            'auto_temp_records': len(auto_temp_data),
            'auto_crucible_records': len(auto_crucible_data),
            'valid_data': True
        })
        
        logger.info(f"成功分析: {os.path.basename(csv_file)}, ΔT={delta_t:.2f}分钟, CCD={ccd_temp_at_completion:.1f}度")
        
    except Exception as e:
        logger.error(f"分析文件失败: {csv_file}, 错误: {e}")
    
    return results

def main():
    """主函数"""
    logger.info("开始正确的永祥BC工段3s数据分析")
    
    # 数据路径
    base_path = r"\\10.0.10.249\root\FreeSpace\B.Jx_BigData\1.Data\永祥BC工段3s数据"
    
    try:
        # 加载和分析数据
        results = load_and_analyze_files(base_path)
        
        if not results:
            logger.error("未找到有效的分析结果")
            return
        
        # 保存结果
        df_results = pd.DataFrame(results)
        df_results.to_csv('correct_analysis_results.csv', index=False, encoding='utf-8-sig')
        
        # 生成统计报告
        generate_correct_report(results)
        
        logger.info("分析完成！")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        raise

def generate_correct_report(results):
    """生成正确的分析报告"""
    if not results:
        return
    
    df = pd.DataFrame(results)
    delta_times = df['delta_T_minutes']
    ccd_temps = df['CCD_temp_at_crucible_completion']
    
    report = []
    report.append("=" * 80)
    report.append("永祥BC工段3s数据正确分析报告")
    report.append("=" * 80)
    report.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    report.append("分析流程:")
    report.append("1. 检查自动定埚位工序完成后的CCD测温值(1444-1452度)")
    report.append("2. 只对CCD测温在有效范围内的数据进行自动控温工序分析")
    report.append("3. 计算副功率关闭到熔液占比达到100%的时间差")
    report.append("")
    
    report.append("分析结果:")
    report.append(f"有效数据量: {len(results)} 个")
    report.append(f"CCD测温范围: {ccd_temps.min():.1f} - {ccd_temps.max():.1f}度")
    report.append(f"时间差统计(分钟):")
    report.append(f"  平均值: {delta_times.mean():.2f}")
    report.append(f"  中位数: {delta_times.median():.2f}")
    report.append(f"  标准差: {delta_times.std():.2f}")
    report.append(f"  最小值: {delta_times.min():.2f}")
    report.append(f"  最大值: {delta_times.max():.2f}")
    report.append("")
    
    report.append("=" * 80)
    
    report_content = "\n".join(report)
    with open('correct_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(report_content)

if __name__ == "__main__":
    main()
