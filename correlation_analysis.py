#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剩料重量与时间差相关性分析
基于已完成的永祥BC工段3s数据分析结果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from datetime import datetime
import warnings
import logging
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('correlation_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def extract_remaining_weight_data(base_path):
    """
    从原始数据中提取剩料重量数据
    """
    logger.info("开始提取剩料重量数据...")
    
    # 读取已分析的有效数据
    valid_results = pd.read_csv('correct_analysis_results.csv')
    logger.info(f"读取到 {len(valid_results)} 个有效工序数据")
    
    # 准备存储结果
    correlation_data = []
    
    for idx, row in valid_results.iterrows():
        try:
            # 构建文件路径
            source_dir = row['source_dir']
            source_file = row['source_file']
            t1_time = pd.to_datetime(row['T1'])
            delta_t = row['delta_T_minutes']
            
            file_path = f"{base_path}\\{source_dir}\\{source_file}"
            
            # 读取原始数据文件
            df = pd.read_csv(file_path, encoding='utf-8')
            df['datetime'] = pd.to_datetime(df['time'])
            
            # 找到T1时间点对应的记录
            # 由于时间可能不完全匹配，找最接近的记录
            time_diff = abs(df['datetime'] - t1_time)
            closest_idx = time_diff.idxmin()
            closest_record = df.loc[closest_idx]
            
            # 提取剩料重量
            remaining_weight = closest_record['剩料重量']
            
            correlation_data.append({
                'source_dir': source_dir,
                'source_file': source_file,
                'T1': t1_time,
                'delta_T_minutes': delta_t,
                'remaining_weight': remaining_weight,
                'time_diff_seconds': time_diff.loc[closest_idx].total_seconds(),
                'CCD_temp': row['CCD_temp_at_crucible_completion']
            })
            
            if (idx + 1) % 10 == 0:
                logger.info(f"已处理 {idx + 1}/{len(valid_results)} 个文件")
                
        except Exception as e:
            logger.error(f"处理文件失败: {source_dir}/{source_file}, 错误: {e}")
            continue
    
    logger.info(f"成功提取 {len(correlation_data)} 个数据点")
    return pd.DataFrame(correlation_data)

def preprocess_data(df):
    """
    数据预处理
    """
    logger.info("开始数据预处理...")
    
    original_count = len(df)
    
    # 检查缺失值
    missing_weight = df['remaining_weight'].isna().sum()
    missing_delta = df['delta_T_minutes'].isna().sum()
    
    logger.info(f"剩料重量缺失值: {missing_weight} 个")
    logger.info(f"时间差缺失值: {missing_delta} 个")
    
    # 移除缺失值
    df_clean = df.dropna(subset=['remaining_weight', 'delta_T_minutes'])
    
    # 检查剩料重量的合理性（假设合理范围为0-10000kg）
    weight_stats = df_clean['remaining_weight'].describe()
    logger.info(f"剩料重量统计: \n{weight_stats}")
    
    # 识别异常值（使用IQR方法）
    def identify_outliers(series, factor=1.5):
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - factor * IQR
        upper_bound = Q3 + factor * IQR
        return (series < lower_bound) | (series > upper_bound)
    
    # 识别剩料重量异常值
    weight_outliers = identify_outliers(df_clean['remaining_weight'])
    delta_outliers = identify_outliers(df_clean['delta_T_minutes'])
    
    logger.info(f"剩料重量异常值: {weight_outliers.sum()} 个")
    logger.info(f"时间差异常值: {delta_outliers.sum()} 个")
    
    # 标记异常值但不删除，用于后续分析
    df_clean['weight_outlier'] = weight_outliers
    df_clean['delta_outlier'] = delta_outliers
    df_clean['any_outlier'] = weight_outliers | delta_outliers
    
    logger.info(f"预处理完成: {original_count} -> {len(df_clean)} 个有效数据点")
    
    return df_clean

def correlation_analysis(df):
    """
    相关性分析
    """
    logger.info("开始相关性分析...")
    
    # 提取有效数据
    valid_data = df.dropna(subset=['remaining_weight', 'delta_T_minutes'])
    
    if len(valid_data) < 3:
        logger.error("有效数据点太少，无法进行相关性分析")
        return None
    
    x = valid_data['remaining_weight']
    y = valid_data['delta_T_minutes']
    
    # 计算皮尔逊相关系数
    pearson_corr, pearson_p = stats.pearsonr(x, y)
    
    # 计算斯皮尔曼等级相关系数
    spearman_corr, spearman_p = stats.spearmanr(x, y)
    
    # 计算置信区间（使用Fisher变换）
    def correlation_confidence_interval(r, n, confidence=0.95):
        z = np.arctanh(r)
        se = 1 / np.sqrt(n - 3)
        alpha = 1 - confidence
        z_critical = stats.norm.ppf(1 - alpha/2)
        
        z_lower = z - z_critical * se
        z_upper = z + z_critical * se
        
        r_lower = np.tanh(z_lower)
        r_upper = np.tanh(z_upper)
        
        return r_lower, r_upper
    
    pearson_ci = correlation_confidence_interval(pearson_corr, len(valid_data))
    
    results = {
        'sample_size': len(valid_data),
        'pearson_correlation': pearson_corr,
        'pearson_p_value': pearson_p,
        'pearson_ci_lower': pearson_ci[0],
        'pearson_ci_upper': pearson_ci[1],
        'spearman_correlation': spearman_corr,
        'spearman_p_value': spearman_p,
        'weight_mean': x.mean(),
        'weight_std': x.std(),
        'weight_range': (x.min(), x.max()),
        'delta_mean': y.mean(),
        'delta_std': y.std(),
        'delta_range': (y.min(), y.max())
    }
    
    logger.info(f"皮尔逊相关系数: {pearson_corr:.4f} (p={pearson_p:.4f})")
    logger.info(f"斯皮尔曼相关系数: {spearman_corr:.4f} (p={spearman_p:.4f})")
    
    return results, valid_data

def create_visualizations(df, results):
    """
    创建可视化图表
    """
    logger.info("创建可视化图表...")
    
    # 设置图表样式
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('剩料重量与时间差相关性分析', fontsize=16, fontweight='bold')
    
    x = df['remaining_weight']
    y = df['delta_T_minutes']
    
    # 1. 散点图
    ax1 = axes[0, 0]
    ax1.scatter(x, y, alpha=0.6, s=50)
    
    # 添加趋势线
    z = np.polyfit(x, y, 1)
    p = np.poly1d(z)
    ax1.plot(x, p(x), "r--", alpha=0.8, linewidth=2)
    
    ax1.set_xlabel('剩料重量 (kg)')
    ax1.set_ylabel('时间差 (分钟)')
    ax1.set_title(f'散点图与趋势线\n皮尔逊相关系数: {results["pearson_correlation"]:.4f}')
    ax1.grid(True, alpha=0.3)
    
    # 2. 剩料重量分布
    ax2 = axes[0, 1]
    ax2.hist(x, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.set_xlabel('剩料重量 (kg)')
    ax2.set_ylabel('频次')
    ax2.set_title('剩料重量分布')
    ax2.grid(True, alpha=0.3)
    
    # 3. 时间差分布
    ax3 = axes[1, 0]
    ax3.hist(y, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
    ax3.set_xlabel('时间差 (分钟)')
    ax3.set_ylabel('频次')
    ax3.set_title('时间差分布')
    ax3.grid(True, alpha=0.3)
    
    # 4. 箱线图对比
    ax4 = axes[1, 1]
    
    # 将剩料重量分组（按四分位数）
    weight_quartiles = pd.qcut(x, q=4, labels=['Q1', 'Q2', 'Q3', 'Q4'])
    df_plot = pd.DataFrame({'weight_group': weight_quartiles, 'delta_T': y})
    
    box_data = [df_plot[df_plot['weight_group'] == group]['delta_T'].values 
                for group in ['Q1', 'Q2', 'Q3', 'Q4']]
    
    ax4.boxplot(box_data, labels=['Q1\n(最低)', 'Q2', 'Q3', 'Q4\n(最高)'])
    ax4.set_xlabel('剩料重量分组')
    ax4.set_ylabel('时间差 (分钟)')
    ax4.set_title('不同剩料重量组的时间差分布')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('correlation_analysis_plots.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    logger.info("可视化图表已保存: correlation_analysis_plots.png")

def generate_report(df, results):
    """
    生成详细的分析报告
    """
    logger.info("生成分析报告...")
    
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("剩料重量与时间差相关性分析报告")
    report_lines.append("=" * 80)
    report_lines.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    # 数据概览
    report_lines.append("1. 数据概览")
    report_lines.append("-" * 40)
    report_lines.append(f"分析样本数: {results['sample_size']} 个")
    report_lines.append(f"数据来源: 74个有效自动控温工序中的T1时间点")
    report_lines.append("")
    
    # 变量统计
    report_lines.append("2. 变量统计描述")
    report_lines.append("-" * 40)
    report_lines.append("剩料重量:")
    report_lines.append(f"  平均值: {results['weight_mean']:.2f} kg")
    report_lines.append(f"  标准差: {results['weight_std']:.2f} kg")
    report_lines.append(f"  范围: {results['weight_range'][0]:.2f} - {results['weight_range'][1]:.2f} kg")
    report_lines.append("")
    report_lines.append("时间差:")
    report_lines.append(f"  平均值: {results['delta_mean']:.2f} 分钟")
    report_lines.append(f"  标准差: {results['delta_std']:.2f} 分钟")
    report_lines.append(f"  范围: {results['delta_range'][0]:.2f} - {results['delta_range'][1]:.2f} 分钟")
    report_lines.append("")
    
    # 相关性分析结果
    report_lines.append("3. 相关性分析结果")
    report_lines.append("-" * 40)
    report_lines.append("皮尔逊相关分析:")
    report_lines.append(f"  相关系数: {results['pearson_correlation']:.4f}")
    report_lines.append(f"  p值: {results['pearson_p_value']:.4f}")
    report_lines.append(f"  95%置信区间: [{results['pearson_ci_lower']:.4f}, {results['pearson_ci_upper']:.4f}]")
    
    # 显著性判断
    if results['pearson_p_value'] < 0.001:
        significance = "极显著 (p < 0.001)"
    elif results['pearson_p_value'] < 0.01:
        significance = "非常显著 (p < 0.01)"
    elif results['pearson_p_value'] < 0.05:
        significance = "显著 (p < 0.05)"
    else:
        significance = "不显著 (p ≥ 0.05)"
    
    report_lines.append(f"  显著性: {significance}")
    report_lines.append("")
    
    report_lines.append("斯皮尔曼等级相关分析:")
    report_lines.append(f"  相关系数: {results['spearman_correlation']:.4f}")
    report_lines.append(f"  p值: {results['spearman_p_value']:.4f}")
    report_lines.append("")
    
    # 相关性强度解释
    abs_corr = abs(results['pearson_correlation'])
    if abs_corr >= 0.8:
        strength = "强相关"
    elif abs_corr >= 0.6:
        strength = "中等相关"
    elif abs_corr >= 0.3:
        strength = "弱相关"
    else:
        strength = "几乎无相关"
    
    direction = "正相关" if results['pearson_correlation'] > 0 else "负相关"
    
    report_lines.append("4. 统计结论")
    report_lines.append("-" * 40)
    report_lines.append(f"相关性强度: {strength}")
    report_lines.append(f"相关性方向: {direction}")
    report_lines.append(f"统计显著性: {significance}")
    report_lines.append("")
    
    # 工艺意义解释
    report_lines.append("5. 工艺意义解释")
    report_lines.append("-" * 40)
    
    if abs_corr >= 0.3 and results['pearson_p_value'] < 0.05:
        if results['pearson_correlation'] > 0:
            report_lines.append("• 剩料重量与时间差存在显著正相关关系")
            report_lines.append("• 剩料重量越大，副功率关闭到熔液占比达到100%的时间越长")
            report_lines.append("• 这可能表明剩料重量影响熔化过程的效率")
        else:
            report_lines.append("• 剩料重量与时间差存在显著负相关关系")
            report_lines.append("• 剩料重量越大，副功率关闭到熔液占比达到100%的时间越短")
            report_lines.append("• 这可能表明更多剩料有助于加快熔化过程")
    else:
        report_lines.append("• 剩料重量与时间差之间无显著相关关系")
        report_lines.append("• 时间差主要受其他工艺参数影响")
        report_lines.append("• 剩料重量对熔化时间的影响较小")
    
    report_lines.append("")
    
    # 建议
    report_lines.append("6. 建议")
    report_lines.append("-" * 40)
    if abs_corr >= 0.3 and results['pearson_p_value'] < 0.05:
        report_lines.append("• 在工艺优化时应考虑剩料重量的影响")
        report_lines.append("• 建议建立剩料重量与工艺时间的预测模型")
        report_lines.append("• 可以根据剩料重量调整工艺参数")
    else:
        report_lines.append("• 剩料重量不是影响工艺时间的主要因素")
        report_lines.append("• 建议重点关注其他工艺参数的优化")
        report_lines.append("• 可以进一步分析温度、功率等参数的影响")
    
    report_lines.append("")
    report_lines.append("=" * 80)
    
    # 保存报告
    report_content = "\n".join(report_lines)
    with open('correlation_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(report_content)
    logger.info("分析报告已保存: correlation_analysis_report.txt")

def main():
    """
    主函数
    """
    logger.info("开始剩料重量与时间差相关性分析")
    
    # 数据路径
    base_path = r"\\10.0.10.249\root\FreeSpace\B.Jx_BigData\1.Data\永祥BC工段3s数据"
    
    try:
        # 1. 提取剩料重量数据
        correlation_df = extract_remaining_weight_data(base_path)
        
        # 保存原始提取数据
        correlation_df.to_csv('correlation_raw_data.csv', index=False, encoding='utf-8-sig')
        logger.info("原始相关性数据已保存: correlation_raw_data.csv")
        
        # 2. 数据预处理
        processed_df = preprocess_data(correlation_df)
        
        # 保存预处理后的数据
        processed_df.to_csv('correlation_processed_data.csv', index=False, encoding='utf-8-sig')
        logger.info("预处理后数据已保存: correlation_processed_data.csv")
        
        # 3. 相关性分析
        results, valid_data = correlation_analysis(processed_df)
        
        if results is None:
            logger.error("相关性分析失败")
            return
        
        # 4. 创建可视化
        create_visualizations(valid_data, results)
        
        # 5. 生成报告
        generate_report(valid_data, results)
        
        logger.info("相关性分析完成！")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()
