#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查CCD相关字段的数据
"""

import pandas as pd
import numpy as np
import os
import glob

def check_ccd_fields():
    """
    检查CCD相关字段的数据分布
    """
    # 获取第一个目录的第一个文件进行检查
    base_path = r"\\10.0.10.249\root\FreeSpace\B.Jx_BigData\1.Data\永祥BC工段3s数据"
    subdirs = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]
    
    if not subdirs:
        print("未找到数据目录")
        return
    
    subdir_path = os.path.join(base_path, subdirs[0])
    csv_files = glob.glob(os.path.join(subdir_path, "*.csv"))
    
    if not csv_files:
        print("未找到CSV文件")
        return
    
    # 读取第一个文件
    csv_file = csv_files[0]
    print(f"检查文件: {os.path.basename(csv_file)}")
    
    try:
        df = pd.read_csv(csv_file, encoding='utf-8')
        print(f"文件记录数: {len(df)}")
        
        # 查找所有CCD相关字段
        ccd_columns = [col for col in df.columns if 'CCD' in col or 'ccd' in col]
        print(f"\n找到的CCD相关字段: {ccd_columns}")
        
        # 检查每个CCD字段的数据
        for col in ccd_columns:
            print(f"\n=== {col} 字段分析 ===")
            values = df[col].dropna()
            if len(values) > 0:
                print(f"非空值数量: {len(values)}")
                print(f"最小值: {values.min():.2f}")
                print(f"最大值: {values.max():.2f}")
                print(f"平均值: {values.mean():.2f}")
                print(f"中位数: {values.median():.2f}")
                print(f"前10个值: {list(values.head(10))}")
            else:
                print("该字段无有效数据")
        
        # 检查工序字段，找到自动控温和自动定埚位的数据
        print(f"\n=== 工序字段分析 ===")
        if '工序' in df.columns:
            # 解析工序编码
            def decode_process_code(process_code):
                if pd.isna(process_code) or process_code == 0:
                    return None, None
                
                try:
                    process_code = int(float(process_code))
                    main_process_code = process_code % 100
                    auxiliary_process_code = process_code // 100
                    
                    # 主工序映射
                    main_map = {17: "自动控温", 19: "自动定埚位"}
                    main_name = main_map.get(main_process_code, f"主工序{main_process_code}")
                    
                    return main_name, auxiliary_process_code
                except (ValueError, TypeError):
                    return None, None
            
            df['主工序'], df['辅助工序'] = zip(*df['工序'].apply(decode_process_code))
            
            # 统计自动控温和自动定埚位的数据
            auto_temp_data = df[df['主工序'] == '自动控温']
            auto_crucible_data = df[df['主工序'] == '自动定埚位']
            
            print(f"自动控温工序记录数: {len(auto_temp_data)}")
            print(f"自动定埚位工序记录数: {len(auto_crucible_data)}")
            
            # 检查自动定埚位完成后的CCD温度
            if len(auto_crucible_data) > 0:
                print(f"\n=== 自动定埚位工序的CCD温度 ===")
                for col in ccd_columns:
                    values = auto_crucible_data[col].dropna()
                    if len(values) > 0:
                        print(f"{col} - 数量: {len(values)}, 范围: {values.min():.1f}-{values.max():.1f}, 平均: {values.mean():.1f}")
            
            # 检查自动控温工序的CCD温度
            if len(auto_temp_data) > 0:
                print(f"\n=== 自动控温工序的CCD温度 ===")
                for col in ccd_columns:
                    values = auto_temp_data[col].dropna()
                    if len(values) > 0:
                        print(f"{col} - 数量: {len(values)}, 范围: {values.min():.1f}-{values.max():.1f}, 平均: {values.mean():.1f}")
        
        # 显示前几行数据中的CCD相关字段
        print(f"\n=== 前5行CCD相关数据 ===")
        if ccd_columns:
            display_cols = ['time', '工序'] + ccd_columns
            available_cols = [col for col in display_cols if col in df.columns]
            print(df[available_cols].head())
        
    except Exception as e:
        print(f"读取文件失败: {e}")

if __name__ == "__main__":
    check_ccd_fields()
