#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成CCD测温范围调整前后的对比分析报告
"""

import pandas as pd
import numpy as np
from datetime import datetime
from scipy import stats

def generate_comparison_report():
    """生成对比分析报告"""
    
    # 读取两个分析结果
    original_results = pd.read_csv('correct_analysis_results.csv')  # 1444-1452度
    revised_results = pd.read_csv('revised_analysis_1447_1452_results.csv')  # 1447-1452度
    
    report_lines = []
    report_lines.append("=" * 100)
    report_lines.append("CCD测温范围调整前后对比分析报告")
    report_lines.append("=" * 100)
    report_lines.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    # 基本信息对比
    report_lines.append("1. 基本信息对比")
    report_lines.append("-" * 60)
    report_lines.append("原分析 (CCD测温范围: 1444-1452度):")
    report_lines.append(f"  有效数据量: {len(original_results)} 个自动控温工序")
    
    report_lines.append("修订分析 (CCD测温范围: 1447-1452度):")
    report_lines.append(f"  有效数据量: {len(revised_results)} 个自动控温工序")
    
    reduction = len(original_results) - len(revised_results)
    reduction_pct = reduction / len(original_results) * 100
    report_lines.append(f"数据变化: 减少 {reduction} 个工序 ({reduction_pct:.1f}%)")
    report_lines.append("")
    
    # CCD测温对比
    report_lines.append("2. CCD测温数据对比")
    report_lines.append("-" * 60)
    orig_ccd = original_results['CCD_temp_at_crucible_completion']
    rev_ccd = revised_results['CCD_temp_at_crucible_completion']
    
    report_lines.append("原分析 (1444-1452度):")
    report_lines.append(f"  CCD测温范围: {orig_ccd.min():.1f} - {orig_ccd.max():.1f}度")
    report_lines.append(f"  CCD测温平均值: {orig_ccd.mean():.1f}度")
    report_lines.append(f"  CCD测温标准差: {orig_ccd.std():.1f}度")
    
    report_lines.append("修订分析 (1447-1452度):")
    report_lines.append(f"  CCD测温范围: {rev_ccd.min():.1f} - {rev_ccd.max():.1f}度")
    report_lines.append(f"  CCD测温平均值: {rev_ccd.mean():.1f}度")
    report_lines.append(f"  CCD测温标准差: {rev_ccd.std():.1f}度")
    
    # 被排除的数据分析
    excluded_data = original_results[~original_results['source_file'].isin(revised_results['source_file']) | 
                                   ~original_results['source_dir'].isin(revised_results['source_dir'])]
    
    if len(excluded_data) > 0:
        excluded_ccd = excluded_data['CCD_temp_at_crucible_completion']
        report_lines.append("被排除的数据:")
        report_lines.append(f"  数量: {len(excluded_data)} 个")
        report_lines.append(f"  CCD测温范围: {excluded_ccd.min():.1f} - {excluded_ccd.max():.1f}度")
        report_lines.append(f"  CCD测温平均值: {excluded_ccd.mean():.1f}度")
    report_lines.append("")
    
    # 时间差统计对比
    report_lines.append("3. 时间差统计对比")
    report_lines.append("-" * 60)
    orig_delta = original_results['delta_T_minutes']
    rev_delta = revised_results['delta_T_minutes']
    
    report_lines.append("原分析 (1444-1452度):")
    report_lines.append(f"  样本数量: {len(orig_delta)}")
    report_lines.append(f"  平均值: {orig_delta.mean():.2f} 分钟")
    report_lines.append(f"  中位数: {orig_delta.median():.2f} 分钟")
    report_lines.append(f"  标准差: {orig_delta.std():.2f} 分钟")
    report_lines.append(f"  最小值: {orig_delta.min():.2f} 分钟")
    report_lines.append(f"  最大值: {orig_delta.max():.2f} 分钟")
    
    report_lines.append("修订分析 (1447-1452度):")
    report_lines.append(f"  样本数量: {len(rev_delta)}")
    report_lines.append(f"  平均值: {rev_delta.mean():.2f} 分钟")
    report_lines.append(f"  中位数: {rev_delta.median():.2f} 分钟")
    report_lines.append(f"  标准差: {rev_delta.std():.2f} 分钟")
    report_lines.append(f"  最小值: {rev_delta.min():.2f} 分钟")
    report_lines.append(f"  最大值: {rev_delta.max():.2f} 分钟")
    
    # 变化分析
    mean_change = rev_delta.mean() - orig_delta.mean()
    median_change = rev_delta.median() - orig_delta.median()
    std_change = rev_delta.std() - orig_delta.std()
    
    report_lines.append("变化分析:")
    report_lines.append(f"  平均值变化: {mean_change:+.2f} 分钟")
    report_lines.append(f"  中位数变化: {median_change:+.2f} 分钟")
    report_lines.append(f"  标准差变化: {std_change:+.2f} 分钟")
    report_lines.append("")
    
    # 异常值对比
    report_lines.append("4. 异常值分析对比")
    report_lines.append("-" * 60)
    
    def analyze_outliers(data):
        q1 = data.quantile(0.25)
        q3 = data.quantile(0.75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        outliers = data[(data < lower_bound) | (data > upper_bound)]
        return outliers, lower_bound, upper_bound
    
    orig_outliers, orig_lower, orig_upper = analyze_outliers(orig_delta)
    rev_outliers, rev_lower, rev_upper = analyze_outliers(rev_delta)
    
    report_lines.append("原分析异常值:")
    report_lines.append(f"  正常范围: [{orig_lower:.2f}, {orig_upper:.2f}] 分钟")
    report_lines.append(f"  异常值数量: {len(orig_outliers)} 个 ({len(orig_outliers)/len(orig_delta)*100:.1f}%)")
    
    report_lines.append("修订分析异常值:")
    report_lines.append(f"  正常范围: [{rev_lower:.2f}, {rev_upper:.2f}] 分钟")
    report_lines.append(f"  异常值数量: {len(rev_outliers)} 个 ({len(rev_outliers)/len(rev_delta)*100:.1f}%)")
    report_lines.append("")
    
    # 分布对比
    report_lines.append("5. 时间差分布对比")
    report_lines.append("-" * 60)
    
    ranges = [
        (0, 20, "0-20分钟"),
        (20, 40, "20-40分钟"),
        (40, 60, "40-60分钟"),
        (60, 100, "60-100分钟"),
        (100, float('inf'), ">100分钟")
    ]
    
    report_lines.append("原分析分布:")
    for min_val, max_val, label in ranges:
        if max_val == float('inf'):
            count = len(orig_delta[orig_delta >= min_val])
        else:
            count = len(orig_delta[(orig_delta >= min_val) & (orig_delta < max_val)])
        percentage = count / len(orig_delta) * 100
        report_lines.append(f"  {label}: {count} 个 ({percentage:.1f}%)")
    
    report_lines.append("修订分析分布:")
    for min_val, max_val, label in ranges:
        if max_val == float('inf'):
            count = len(rev_delta[rev_delta >= min_val])
        else:
            count = len(rev_delta[(rev_delta >= min_val) & (rev_delta < max_val)])
        percentage = count / len(rev_delta) * 100
        report_lines.append(f"  {label}: {count} 个 ({percentage:.1f}%)")
    report_lines.append("")
    
    # 剩料重量相关性对比
    report_lines.append("6. 剩料重量相关性对比")
    report_lines.append("-" * 60)
    
    # 原分析相关性（从之前的相关性分析结果）
    report_lines.append("原分析 (1444-1452度):")
    report_lines.append("  皮尔逊相关系数: 0.0374 (p=0.7517)")
    report_lines.append("  相关性结论: 无显著相关关系")
    
    # 修订分析相关性
    if 'remaining_weight' in revised_results.columns:
        rev_weights = revised_results['remaining_weight']
        pearson_corr, pearson_p = stats.pearsonr(rev_weights, rev_delta)
        spearman_corr, spearman_p = stats.spearmanr(rev_weights, rev_delta)
        
        report_lines.append("修订分析 (1447-1452度):")
        report_lines.append(f"  剩料重量范围: {rev_weights.min():.1f} - {rev_weights.max():.1f} kg")
        report_lines.append(f"  剩料重量平均值: {rev_weights.mean():.1f} kg")
        report_lines.append(f"  剩料重量变异系数: {rev_weights.std()/rev_weights.mean()*100:.2f}%")
        report_lines.append(f"  皮尔逊相关系数: {pearson_corr:.4f} (p={pearson_p:.4f})")
        report_lines.append(f"  斯皮尔曼相关系数: {spearman_corr:.4f} (p={spearman_p:.4f})")
        
        if pearson_p < 0.05:
            significance = "显著"
        else:
            significance = "不显著"
        report_lines.append(f"  相关性结论: {significance}")
    report_lines.append("")
    
    # 统计显著性检验
    report_lines.append("7. 统计显著性检验")
    report_lines.append("-" * 60)
    
    # 使用Mann-Whitney U检验比较两组时间差
    statistic, p_value = stats.mannwhitneyu(orig_delta, rev_delta, alternative='two-sided')
    
    report_lines.append("Mann-Whitney U检验 (比较两组时间差):")
    report_lines.append(f"  检验统计量: {statistic}")
    report_lines.append(f"  p值: {p_value:.4f}")
    
    if p_value < 0.05:
        significance = "显著差异"
    else:
        significance = "无显著差异"
    report_lines.append(f"  结论: 两组数据间{significance}")
    report_lines.append("")
    
    # 结论和建议
    report_lines.append("8. 结论和建议")
    report_lines.append("-" * 60)
    report_lines.append("主要发现:")
    report_lines.append(f"• CCD测温范围从1444-1452度调整为1447-1452度后，有效数据减少了{reduction_pct:.1f}%")
    report_lines.append(f"• 时间差的中位数从{orig_delta.median():.1f}分钟变为{rev_delta.median():.1f}分钟")
    report_lines.append(f"• 异常值比例从{len(orig_outliers)/len(orig_delta)*100:.1f}%变为{len(rev_outliers)/len(rev_delta)*100:.1f}%")
    report_lines.append("• 剩料重量与时间差仍无显著相关关系")
    report_lines.append("")
    
    report_lines.append("工艺意义:")
    if reduction_pct > 30:
        report_lines.append("• 温度范围收紧导致大量数据被排除，可能过于严格")
    else:
        report_lines.append("• 温度范围收紧合理，提高了数据质量")
    
    if abs(median_change) < 5:
        report_lines.append("• 典型工艺时间基本保持稳定")
    else:
        report_lines.append(f"• 典型工艺时间发生了{abs(median_change):.1f}分钟的变化")
    
    report_lines.append("")
    
    report_lines.append("建议:")
    if reduction_pct > 40:
        report_lines.append("• 考虑适当放宽CCD测温范围，避免过度排除有效数据")
    else:
        report_lines.append("• 当前CCD测温范围(1447-1452度)是合理的")
    
    report_lines.append("• 继续监控CCD测温的稳定性")
    report_lines.append("• 重点关注异常时间差的工序")
    report_lines.append("• 建立基于多参数的工艺优化模型")
    report_lines.append("")
    
    report_lines.append("=" * 100)
    
    # 保存报告
    report_content = "\n".join(report_lines)
    with open('comparison_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("对比分析报告已生成!")
    print("\n" + report_content)

if __name__ == "__main__":
    generate_comparison_report()
